import React, { useState } from "react";

type TimeframeType = "1D" | "7D" | "1M" | "1Y";

const AssetsBalance = () => {
  const [selectedTimeframe, setSelectedTimeframe] =
    useState<TimeframeType>("1Y");

  const timeframes: TimeframeType[] = ["1D", "7D", "1M", "1Y"];
  const balance: number = 158255.25;

  const handleTimeframeChange = (timeframe: TimeframeType): void => {
    setSelectedTimeframe(timeframe);
  };

  return (
    <div className="flex flex-col gap-4 text-white">
      <div className="flex flex-col gap-2 rounded-xl relative px-4 pb-4 pt-2">
        <div className="relative">
          {/* Balance Section */}
          <div className="flex flex-col text-center text-white gap-0.5">
            <p className="font-medium text-base">Your Balance</p>
            <p className="text-5xl font-bold tracking-tight">
              ${balance.toLocaleString()}
            </p>
            <p className="text-[#7FC9A0] font-bold text-3xl">
              + 2.4% <span className="font-normal">($873.12)</span>
            </p>
            <p className="text-xs font-normal">Yesterday's Profit</p>
          </div>

          {/* Curved Line Container */}
          <div className="relative mt-10">
            {/* Simple Curved Line */}
            <div className="h-20 relative flex items-end justify-center">
              <svg
                width="280"
                height="80"
                viewBox="0 0 280 80"
                className="absolute bottom-0"
              >
                <defs>
                  <linearGradient
                    id="curveGradient"
                    x1="0%"
                    y1="0%"
                    x2="0%"
                    y2="100%"
                  >
                    <stop offset="0%" stopColor="#7FC9A0" stopOpacity="0.3" />
                    <stop offset="100%" stopColor="#7FC9A0" stopOpacity="0" />
                  </linearGradient>
                </defs>

                {/* Curved path */}
                <path
                  d="M 20 60 Q 140 20 260 60"
                  stroke="#7FC9A0"
                  strokeWidth="2"
                  fill="none"
                  className="drop-shadow-sm"
                />

                {/* Fill area under curve */}
                <path
                  d="M 20 60 Q 140 20 260 60 L 260 80 L 20 80 Z"
                  fill="url(#curveGradient)"
                />
              </svg>
            </div>

            {/* Hexagonal Timeline Buttons positioned on the curve */}
            <div className="absolute inset-0 pointer-events-none">
              {timeframes.map((tf: TimeframeType, index) => {
                // Position buttons along the curve
                const positions = [
                  { left: "15%", bottom: "25px" }, // 1D
                  { left: "35%", bottom: "10px" }, // 7D
                  { left: "65%", bottom: "10px" }, // 1M
                  { left: "85%", bottom: "25px" }, // 1Y
                ];

                return (
                  <button
                    key={tf}
                    type="button"
                    onClick={() => handleTimeframeChange(tf)}
                    className="absolute w-8 h-8 cursor-pointer transition-all duration-200 hover:scale-110 pointer-events-auto"
                    style={{
                      left: positions[index].left,
                      bottom: positions[index].bottom,
                    }}
                  >
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      className="w-full h-full"
                    >
                      <polygon
                        points="16,2 28,8 28,24 16,30 4,24 4,8"
                        fill={selectedTimeframe === tf ? "#7FC9A0" : "none"}
                        fillOpacity={selectedTimeframe === tf ? "0.2" : "0"}
                        stroke={
                          selectedTimeframe === tf ? "#7FC9A0" : "#ffffff60"
                        }
                        strokeWidth="1.5"
                      />
                    </svg>
                    <span
                      className={`absolute inset-0 flex items-center justify-center text-xs font-medium transition-colors duration-200 ${
                        selectedTimeframe === tf
                          ? "text-[#7FC9A0]"
                          : "text-white/70"
                      }`}
                    >
                      {tf}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetsBalance;
