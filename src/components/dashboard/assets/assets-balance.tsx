import React, { useState } from "react";

type TimeframeType = "1D" | "7D" | "1M" | "1Y";

const AssetsBalance = () => {
  const [selectedTimeframe, setSelectedTimeframe] =
    useState<TimeframeType>("1Y");

  const timeframes: TimeframeType[] = ["1D", "7D", "1M", "1Y"];
  const balance: number = 158255.25;

  const handleTimeframeChange = (timeframe: TimeframeType): void => {
    setSelectedTimeframe(timeframe);
  };

  return (
    <div className="flex flex-col gap-4 text-white">
      <div className="flex flex-col gap-2 rounded-xl relative px-4 pb-4 pt-2">
        <div className="relative">
          {/* Balance Section */}
          <div className="flex flex-col text-center text-white gap-0.5">
            <p className="font-medium text-base">Your Balance</p>
            <p className="text-5xl font-bold tracking-tight">
              ${balance.toLocaleString()}
            </p>
            <p className="text-[#7FC9A0] font-bold text-3xl">
              + 2.4% <span className="font-normal">($873.12)</span>
            </p>
            <p className="text-xs font-normal">Yesterday's Profit</p>
          </div>

          {/* Curved Line Container */}
          <div className="relative mt-10 h-24">
            {/* Longer Inverted Curved Line */}
            <div className="h-full relative flex items-center justify-center">
              <svg
                width="100%"
                height="100%"
                viewBox="0 0 400 100"
                className="absolute inset-0"
                preserveAspectRatio="xMidYMid meet"
              >
                <defs>
                  <linearGradient
                    id="curveGradient"
                    x1="0%"
                    y1="100%"
                    x2="0%"
                    y2="0%"
                  >
                    <stop offset="0%" stopColor="#7FC9A0" stopOpacity="0.2" />
                    <stop offset="100%" stopColor="#7FC9A0" stopOpacity="0" />
                  </linearGradient>
                </defs>

                {/* Longer inverted curved path */}
                <path
                  d="M 20 30 Q 200 80 380 30"
                  stroke="#7FC9A0"
                  strokeWidth="2"
                  fill="none"
                  className="drop-shadow-sm"
                />

                {/* Fill area above curve */}
                <path
                  d="M 20 30 Q 200 80 380 30 L 380 0 L 20 0 Z"
                  fill="url(#curveGradient)"
                />
              </svg>
            </div>

            {/* Hexagonal Timeline Buttons positioned on the curve */}
            <div className="absolute inset-0">
              {timeframes.map((tf: TimeframeType, index) => {
                // Calculate positions along the curve
                const positions = [
                  { left: "15%", bottom: "45%" }, // 1D
                  { left: "35%", bottom: "25%" }, // 7D
                  { left: "65%", bottom: "25%" }, // 1M
                  { left: "85%", bottom: "45%" }, // 1Y
                ];

                return (
                  <button
                    key={tf}
                    type="button"
                    onClick={() => handleTimeframeChange(tf)}
                    className="absolute w-12 h-12 cursor-pointer transition-all duration-200 hover:scale-110 transform -translate-x-1/2 -translate-y-1/2"
                    style={{
                      left: positions[index].left,
                      bottom: positions[index].bottom,
                    }}
                  >
                    <svg
                      width="48"
                      height="48"
                      viewBox="0 0 48 48"
                      className="w-full h-full"
                    >
                      <polygon
                        points="24,4 42,13 42,35 24,44 6,35 6,13"
                        fill={
                          selectedTimeframe === tf
                            ? "#7FC9A0"
                            : "rgba(0,0,0,0.3)"
                        }
                        fillOpacity={selectedTimeframe === tf ? "0.4" : "0.6"}
                        stroke={
                          selectedTimeframe === tf ? "#7FC9A0" : "#ffffff80"
                        }
                        strokeWidth="2"
                      />
                    </svg>
                    <span
                      className={`absolute inset-0 flex items-center justify-center text-sm font-bold transition-colors duration-200 ${
                        selectedTimeframe === tf
                          ? "text-[#7FC9A0]"
                          : "text-white"
                      }`}
                    >
                      {tf}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetsBalance;
