import React, { useState } from "react";

type TimeframeType = "1D" | "7D" | "1M" | "1Y";

const AssetsBalance = () => {
  const [selectedTimeframe, setSelectedTimeframe] =
    useState<TimeframeType>("1Y");

  const timeframes: TimeframeType[] = ["1D", "7D", "1M", "1Y"];
  const balance: number = 158255.25;

  const handleTimeframeChange = (timeframe: TimeframeType): void => {
    setSelectedTimeframe(timeframe);
  };

  return (
    <div className="flex flex-col gap-4 text-white">
      <div className="flex flex-col gap-2 rounded-xl relative px-4 pb-4 pt-2">
        <div className="relative">
          {/* Balance Section */}
          <div className="flex flex-col text-center text-white gap-0.5">
            <p className="font-medium text-base">Your Balance</p>
            <p className="text-5xl font-bold tracking-tight">
              ${balance.toLocaleString()}
            </p>
            <p className="text-[#7FC9A0] font-bold text-3xl">
              + 2.4% <span className="font-normal">($873.12)</span>
            </p>
            <p className="text-xs font-normal">Yesterday's Profit</p>
          </div>

          {/* Curved Line Container */}
          <div className="relative mt-10">
            {/* Inverted Curved Line */}
            <div className="h-20 relative flex items-center justify-center">
              <svg
                width="300"
                height="80"
                viewBox="0 0 300 80"
                className="absolute"
              >
                <defs>
                  <linearGradient
                    id="curveGradient"
                    x1="0%"
                    y1="100%"
                    x2="0%"
                    y2="0%"
                  >
                    <stop offset="0%" stopColor="#7FC9A0" stopOpacity="0.3" />
                    <stop offset="100%" stopColor="#7FC9A0" stopOpacity="0" />
                  </linearGradient>
                </defs>

                {/* Inverted curved path (smile shape) */}
                <path
                  d="M 30 20 Q 150 60 270 20"
                  stroke="#7FC9A0"
                  strokeWidth="2"
                  fill="none"
                  className="drop-shadow-sm"
                />

                {/* Fill area above curve */}
                <path
                  d="M 30 20 Q 150 60 270 20 L 270 0 L 30 0 Z"
                  fill="url(#curveGradient)"
                />
              </svg>
            </div>

            {/* Hexagonal Timeline Buttons arranged in a line */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex items-center gap-4">
              {timeframes.map((tf: TimeframeType) => (
                <button
                  key={tf}
                  type="button"
                  onClick={() => handleTimeframeChange(tf)}
                  className="relative w-10 h-10 cursor-pointer transition-all duration-200 hover:scale-110"
                >
                  <svg
                    width="40"
                    height="40"
                    viewBox="0 0 40 40"
                    className="w-full h-full"
                  >
                    <polygon
                      points="20,3 35,11 35,29 20,37 5,29 5,11"
                      fill={selectedTimeframe === tf ? "#7FC9A0" : "none"}
                      fillOpacity={selectedTimeframe === tf ? "0.3" : "0"}
                      stroke={
                        selectedTimeframe === tf ? "#7FC9A0" : "#ffffff60"
                      }
                      strokeWidth="2"
                    />
                  </svg>
                  <span
                    className={`absolute inset-0 flex items-center justify-center text-sm font-medium transition-colors duration-200 ${
                      selectedTimeframe === tf
                        ? "text-[#7FC9A0]"
                        : "text-white/80"
                    }`}
                  >
                    {tf}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetsBalance;
