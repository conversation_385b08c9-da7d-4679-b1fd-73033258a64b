import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  YAxi<PERSON>,
  ResponsiveContainer,
  Tooltip,
  Area,
  AreaChart,
} from "recharts";

// Type definitions
interface ChartDataPoint {
  price: number;
  day: number;
}

interface CustomActiveDotProps {
  cx?: number;
  cy?: number;
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: ChartDataPoint;
  }>;
  label?: string | number;
}

type TimeframeType = "1D" | "7D" | "1M" | "1Y";

// Generate realistic chart data
const generateChartData = (timeframe: TimeframeType): ChartDataPoint[] => {
  const data: ChartDataPoint[] = [];
  const basePrice: number = 158255.25;
  const currentPrice: number = 158255.25;

  let days: number;
  switch (timeframe) {
    case "1D":
      days = 24; // 24 hours
      break;
    case "7D":
      days = 7;
      break;
    case "1M":
      days = 30;
      break;
    case "1Y":
      days = 365;
      break;
    default:
      days = 365;
  }

  const totalGrowth: number = currentPrice / basePrice;
  const dailyGrowthRate: number = Math.pow(totalGrowth, 1 / days);

  for (let i = 0; i < days; i++) {
    const volatility: number =
      0.05 * Math.sin(i * 0.3) + (Math.random() - 0.5) * 0.03;
    const trendPrice: number = basePrice * Math.pow(dailyGrowthRate, i);
    const price: number = trendPrice * (1 + volatility);

    data.push({
      price: Math.max(price, basePrice * 0.8),
      day: i,
    });
  }

  return data;
};

const AssetsBalance = () => {
  const [selectedTimeframe, setSelectedTimeframe] =
    useState<TimeframeType>("1Y");
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [hoveredPoint, setHoveredPoint] = useState<ChartDataPoint | null>(null);
  const [currentPrice, setCurrentPrice] = useState<number>(158255.25);

  useEffect(() => {
    const data: ChartDataPoint[] = generateChartData(selectedTimeframe);
    setChartData(data);
    if (data.length > 0) {
      setCurrentPrice(data[data.length - 1].price);
    }
  }, [selectedTimeframe]);

  const timeframes: TimeframeType[] = ["1D", "7D", "1M", "1Y"];
  const balance: number = 158255.25;

  const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      setHoveredPoint(data);
    }
    return null;
  };

  const CustomActiveDot: React.FC<CustomActiveDotProps> = (props) => {
    const { cx, cy } = props;
    return (
      <g>
        <circle
          cx={cx}
          cy={cy}
          r={10}
          fill="none"
          stroke="#7FC9A0"
          strokeWidth={1}
          opacity={0.3}
        />
        <circle
          cx={cx}
          cy={cy}
          r={6}
          fill="none"
          stroke="#7FC9A0"
          strokeWidth={1}
          opacity={0.5}
        />
        <circle
          cx={cx}
          cy={cy}
          r={3}
          fill="#7FC9A0"
          stroke="#ffffff"
          strokeWidth={2}
        />
      </g>
    );
  };

  const handleTimeframeChange = (timeframe: TimeframeType): void => {
    setSelectedTimeframe(timeframe);
  };

  const handleMouseLeave = (): void => {
    setHoveredPoint(null);
  };

  return (
    <div className="flex flex-col gap-4 text-white">
      <div className="flex flex-col gap-2 rounded-xl relative px-4 pb-4 pt-2">
        <div className="relative">
          {/* Balance Section */}
          <div className="flex flex-col text-center text-white gap-0.5">
            <p className="font-medium text-base">Your Balance</p>
            <p className="text-5xl font-bold tracking-tight">
              ${balance.toLocaleString()}
            </p>
            <p className="text-[#7FC9A0] font-bold text-3xl">
              + 2.4% <span className="font-normal">($873.12)</span>
            </p>
            <p className="text-xs font-normal">Yesterday's Profit</p>
          </div>

          {/* Chart Container */}
          <div className="relative">
            {/* Chart */}
            <div className="h-40 relative mt-10">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={chartData}
                  margin={{ left: 20, right: 20, top: 20, bottom: 20 }}
                  onMouseLeave={handleMouseLeave}
                >
                  <defs>
                    <linearGradient
                      id="areaGradient"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="0%" stopColor="#7FC9A0" stopOpacity={0.4} />
                      <stop
                        offset="50%"
                        stopColor="#7FC9A0"
                        stopOpacity={0.2}
                      />
                      <stop offset="100%" stopColor="#7FC9A0" stopOpacity={0} />
                    </linearGradient>
                    <filter id="lineGlow">
                      <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                      <feMerge>
                        <feMergeNode in="coloredBlur" />
                        <feMergeNode in="SourceGraphic" />
                      </feMerge>
                    </filter>
                  </defs>

                  <XAxis
                    dataKey="day"
                    axisLine={false}
                    tickLine={false}
                    tick={false}
                  />
                  <YAxis hide />

                  <Tooltip content={<CustomTooltip />} cursor={false} />

                  <Area
                    type="monotone"
                    dataKey="price"
                    stroke="#7FC9A0"
                    strokeWidth={3}
                    fill="url(#areaGradient)"
                    filter="url(#lineGlow)"
                    dot={false}
                    activeDot={<CustomActiveDot />}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>

            {/* Hexagonal Timeline Buttons */}
            <div className="absolute bottom-0 right-0 flex items-center gap-2">
              {timeframes.map((tf: TimeframeType) => (
                <button
                  key={tf}
                  type="button"
                  onClick={() => handleTimeframeChange(tf)}
                  className="relative w-9 h-9 cursor-pointer transition-all duration-200 hover:scale-105"
                >
                  <svg
                    width="36"
                    height="36"
                    viewBox="0 0 36 36"
                    className="absolute inset-0"
                  >
                    <polygon
                      points="18,2 31,10 31,26 18,34 5,26 5,10"
                      fill="none"
                      stroke={
                        selectedTimeframe === tf ? "#7FC9A0" : "#ffffff40"
                      }
                      strokeWidth="1.5"
                    />
                    {selectedTimeframe === tf && (
                      <polygon
                        points="18,2 31,10 31,26 18,34 5,26 5,10"
                        fill="#7FC9A0"
                        fillOpacity="0.1"
                      />
                    )}
                  </svg>
                  <span
                    className={`absolute inset-0 flex items-center justify-center text-xs font-medium transition-colors duration-200 ${
                      selectedTimeframe === tf
                        ? "text-[#7FC9A0]"
                        : "text-white/60"
                    }`}
                  >
                    {tf}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetsBalance;
