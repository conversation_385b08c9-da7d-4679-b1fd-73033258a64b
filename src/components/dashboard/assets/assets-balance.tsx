import React, { useState } from "react";

type TimeframeType = "1D" | "7D" | "1M" | "1Y";

const AssetsBalance = () => {
  const [selectedTimeframe, setSelectedTimeframe] =
    useState<TimeframeType>("1Y");

  const timeframes: TimeframeType[] = ["1D", "7D", "1M", "1Y"];
  const balance: number = 158255.25;

  const handleTimeframeChange = (timeframe: TimeframeType): void => {
    setSelectedTimeframe(timeframe);
  };

  return (
    <div className="flex flex-col gap-4 text-white">
      <div className="flex flex-col gap-2 rounded-xl relative px-4 pb-4 pt-2">
        <div className="relative">
          {/* Balance Section */}
          <div className="flex flex-col text-center text-white gap-0.5">
            <p className="font-medium text-base">Your Balance</p>
            <p className="text-5xl font-bold tracking-tight">
              ${balance.toLocaleString()}
            </p>
            <p className="text-[#7FC9A0] font-bold text-3xl">
              + 2.4% <span className="font-normal">($873.12)</span>
            </p>
            <p className="text-xs font-normal">Yesterday's Profit</p>
          </div>

          {/* Curved Line Container */}
          <div className="relative mt-10 h-20">
            {/* Nearly Complete Semicircle */}
            <div className="h-full relative flex items-center justify-center">
              <svg
                width="100%"
                height="100%"
                viewBox="0 0 400 80"
                className="absolute inset-0"
                preserveAspectRatio="xMidYMid meet"
              >
                <defs>
                  <linearGradient
                    id="curveGradient"
                    x1="0%"
                    y1="100%"
                    x2="0%"
                    y2="0%"
                  >
                    <stop offset="0%" stopColor="#7FC9A0" stopOpacity="0.2" />
                    <stop offset="100%" stopColor="#7FC9A0" stopOpacity="0" />
                  </linearGradient>
                </defs>

                {/* More curved path that passes through timeline buttons */}
                <path
                  d="M 20 25 Q 100 65 200 65 T 380 25"
                  stroke="#7FC9A0"
                  strokeWidth="2"
                  fill="none"
                  className="drop-shadow-sm"
                />

                {/* Fill area above curve */}
                <path
                  d="M 20 25 Q 100 65 200 65 T 380 25 L 380 0 L 20 0 Z"
                  fill="url(#curveGradient)"
                />
              </svg>
            </div>

            {/* Hexagonal Timeline Buttons positioned along the semicircle */}
            <div className="absolute inset-0">
              {timeframes.map((tf: TimeframeType, index) => {
                // Calculate positions on the more curved path
                // Position buttons where the curve passes through them
                const positions = [
                  { x: 80, y: 25 }, // 1D - on curve left edge
                  { x: 160, y: 65 }, // 7D - on curve left-center (deeper)
                  { x: 240, y: 65 }, // 1M - on curve right-center (deeper)
                  { x: 320, y: 25 }, // 1Y - on curve right edge
                ];

                const position = positions[index];

                // Convert to percentages for positioning
                const leftPercent = (position.x / 400) * 100;
                const topPercent = (position.y / 80) * 100;

                return (
                  <button
                    key={tf}
                    type="button"
                    onClick={() => handleTimeframeChange(tf)}
                    className="absolute w-10 h-10 cursor-pointer transition-all duration-200 hover:scale-110 transform -translate-x-1/2 -translate-y-1/2"
                    style={{
                      left: `${leftPercent}%`,
                      top: `${topPercent}%`,
                    }}
                  >
                    <svg
                      width="40"
                      height="40"
                      viewBox="0 0 40 40"
                      className="w-full h-full"
                    >
                      <polygon
                        points="20,3 35,11 35,29 20,37 5,29 5,11"
                        fill={
                          selectedTimeframe === tf
                            ? "#7FC9A0"
                            : "rgba(0,0,0,0.4)"
                        }
                        fillOpacity={selectedTimeframe === tf ? "0.5" : "0.7"}
                        stroke={
                          selectedTimeframe === tf ? "#7FC9A0" : "#ffffff90"
                        }
                        strokeWidth="2"
                      />
                    </svg>
                    <span
                      className={`absolute inset-0 flex items-center justify-center text-xs font-bold transition-colors duration-200 ${
                        selectedTimeframe === tf
                          ? "text-[#7FC9A0]"
                          : "text-white"
                      }`}
                    >
                      {tf}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetsBalance;
