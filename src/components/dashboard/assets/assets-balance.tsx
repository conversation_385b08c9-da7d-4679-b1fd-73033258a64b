import React, { useState } from "react";

type TimeframeType = "1D" | "7D" | "1M" | "1Y";

const AssetsBalance = () => {
  const [selectedTimeframe, setSelectedTimeframe] =
    useState<TimeframeType>("1Y");

  const timeframes: TimeframeType[] = ["1D", "7D", "1M", "1Y"];
  const balance: number = 158255.25;

  const handleTimeframeChange = (timeframe: TimeframeType): void => {
    setSelectedTimeframe(timeframe);
  };

  return (
    <div className="flex flex-col gap-4 text-white">
      <div className="flex flex-col gap-2 rounded-xl relative px-4 pb-4 pt-2">
        <div className="relative">
          {/* Balance Section */}
          <div className="flex flex-col text-center text-white gap-0.5">
            <p className="font-medium text-base">Your Balance</p>
            <p className="text-5xl font-bold tracking-tight">
              ${balance.toLocaleString()}
            </p>
            <p className="text-[#7FC9A0] font-bold text-3xl">
              + 2.4% <span className="font-normal">($873.12)</span>
            </p>
            <p className="text-xs font-normal">Yesterday's Profit</p>
          </div>

          {/* Curved Line Container */}
          <div className="relative -mt-4 h-20">
            {/* Nearly Complete Semicircle */}
            <div className="h-full relative flex items-center justify-center">
              <svg
                width="100%"
                height="100%"
                viewBox="0 0 400 80"
                className="absolute inset-0"
                preserveAspectRatio="xMidYMid meet"
              >
                <defs>
                  <linearGradient
                    id="curveGradient"
                    x1="0%"
                    y1="100%"
                    x2="0%"
                    y2="0%"
                  >
                    <stop offset="0%" stopColor="#7FC9A0" stopOpacity="0.2" />
                    <stop offset="100%" stopColor="#7FC9A0" stopOpacity="0" />
                  </linearGradient>
                </defs>

                {/* Extra curved path that passes through timeline buttons */}
                <path
                  d="M 20 10 Q 100 78 200 78 T 380 10"
                  stroke="#7FC9A0"
                  strokeWidth="2"
                  fill="none"
                  className="drop-shadow-sm"
                />

                {/* Fill area above curve */}
                <path
                  d="M 20 10 Q 100 78 200 78 T 380 10 L 380 0 L 20 0 Z"
                  fill="url(#curveGradient)"
                />
              </svg>
            </div>

            {/* Hexagonal Timeline Buttons positioned along the semicircle */}
            <div className="absolute inset-0">
              {timeframes.map((tf: TimeframeType, index) => {
                // Calculate positions on the much more curved path
                // Position buttons where the curve passes through them
                const positions = [
                  { x: 120, y: 60 }, // 1D - on curve left edge (higher)
                  { x: 180, y: 72 }, // 7D - on curve left-center (much deeper)
                  { x: 240, y: 70 }, // 1M - on curve right-center (much deeper)
                  { x: 300, y: 60 }, // 1Y - on curve right edge (higher)
                ];

                const position = positions[index];

                // Convert to percentages for positioning
                const leftPercent = (position.x / 400) * 100;
                const topPercent = (position.y / 80) * 100;

                return (
                  <button
                    key={tf}
                    type="button"
                    onClick={() => handleTimeframeChange(tf)}
                    className="absolute w-10 h-10 cursor-pointer transition-all duration-200 hover:scale-110 transform -translate-x-1/2 -translate-y-1/2"
                    style={{
                      left: `${leftPercent}%`,
                      top: `${topPercent}%`,
                    }}
                  >
                    <svg
                      width="40"
                      height="40"
                      viewBox="0 0 40 40"
                      className="w-full h-full"
                    >
                      <defs>
                        <radialGradient
                          id={`activeGradient-${tf}`}
                          cx="48.71%"
                          cy="10.66%"
                          r="151.38%"
                        >
                          <stop offset="0%" stopColor="#7FC9A0" />
                          <stop offset="100%" stopColor="#508A69" />
                        </radialGradient>
                      </defs>
                      <polygon
                        points="20,3 35,11 35,29 20,37 5,29 5,11"
                        fill={
                          selectedTimeframe === tf
                            ? `url(#activeGradient-${tf})`
                            : "#0C341D"
                        }
                        fillOpacity={selectedTimeframe === tf ? "1" : "0.7"}
                        stroke={"#508968"}
                        strokeWidth="2"
                      />
                    </svg>
                    <span
                      className={`absolute inset-0 flex items-center justify-center text-xs font-bold transition-colors duration-200 ${
                        selectedTimeframe === tf
                          ? "text-[#FFFFFFC2]"
                          : "text-[#FFFFFFC2]"
                      }`}
                    >
                      {tf}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetsBalance;
