import { Calculator, FileChartColumnIncreasing } from "lucide-react";
import React from "react";
import AssetCard, { AssetTransaction } from "./asset-card";

const assets: AssetTransaction[] = [
  {
    id: "1",
    date: "04-05-2025",
    type: "sell",
    amount: 200000,
    currency: "USDP",
    total: 200000,
    totalCurrency: "USDC",
    exchangeRate: "1USDP = 10.5546 USDC",
    orderId: "23456221",
    profitLoss: {
      type: "loss",
      percentage: 386.5,
      amount: 23006.58,
      period: "11 Day",
    },
  },
  {
    id: "2",
    date: "04-05-2025",
    type: "buy",
    amount: 200000,
    currency: "USDP",
    total: 200000,
    totalCurrency: "USDC",
    exchangeRate: "1USDP = 10.5546 USDC",
    orderId: "23456221",
    profitLoss: {
      type: "profit",
      percentage: 386.5,
      amount: 23006.58,
      period: "4 Day",
    },
  },
];

const UserAssets = () => {
  return (
    <div className="flex flex-col gap-10">
      <div className="flex flex-col ">
        <p className="text-xs font-normal text-white">Your Profit in 41 days</p>
        <div className="flex items-end justify-between">
          <div className="flex items-end gap-3">
            <p className="text-2xl font-bold text-white">$141,073.12</p>
            <p className="text-base font-bold text-[#7FC9A0]">+ 386.5%</p>
          </div>
          <p className="font-bold text-sm text-white">13 Total orders</p>
        </div>
      </div>
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-4">
          {assets.map((asset) => (
            <AssetCard key={asset.id} transaction={asset} />
          ))}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <FileChartColumnIncreasing size={14} className="text-white" />
            <span className="text-white font-medium text-sm">
              Full Statistic
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Calculator size={14} className="text-white" />
            <span className="text-white font-medium text-sm">
              Profit Calculator
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserAssets;
