import { Calculator, FileChartColumnIncreasing } from "lucide-react";
import React from "react";

const assets: Asset[] = [];

const UserAssets = () => {
  return (
    <div className="flex flex-col gap-10">
      <div className="flex flex-col ">
        <p className="text-xs font-normal text-white">Your Profit in 41 days</p>
        <div className="flex items-end justify-between">
          <div className="flex items-end gap-3">
            <p className="text-2xl font-bold text-white">$141,073.12</p>
            <p className="text-base font-bold text-[#7FC9A0]">+ 386.5%</p>
          </div>
          <p className="font-bold text-sm text-white">13 Total orders</p>
        </div>
      </div>
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-4">
            {assets.map((asset, index) => (
                <div></div>

            ))}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <FileChartColumnIncreasing size={14} className="text-white" />
            <span className="text-white font-medium text-sm">
              Full Statistic
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Calculator size={14} className="text-white" />
            <span className="text-white font-medium text-sm">
              Profit Calculator
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserAssets;
