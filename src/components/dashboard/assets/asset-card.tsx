import React from "react";

export type AssetTransaction = {
  id: string;
  date: string;
  type: "buy" | "sell";
  amount: number;
  currency: string;
  total: number;
  totalCurrency: string;
  exchangeRate: string;
  orderId: string;
  profitLoss: {
    type: "profit" | "loss";
    percentage: number;
    amount: number;
    period: string;
  };
};

type AssetCardProps = {
  transaction: AssetTransaction;
};

const AssetCard: React.FC<AssetCardProps> = ({ transaction }) => {
  const isProfit = transaction.profitLoss.type === "profit";
  const profitLossColor = isProfit ? "#7FC9A0" : "#FF6B6B";
  const profitLossSign = isProfit ? "+" : "-";

  return (
    <div className="relative">
      {/* Date positioned on the border */}
      <div className="absolute -top-2 left-4 bg-[#0C341D] px-2 text-xs text-[#FFFFFF80] z-10">
        {transaction.date}
      </div>

      <div
        className="rounded-xl p-3 border border-[#508968] bg-[#0C341D] bg-opacity-40"
        style={{
          background: "rgba(12, 52, 29, 0.4)",
          border: "1px solid #508968",
        }}
      >
        <div className="w-full flex flex-col">
          <div className="flex items-center gap-2 justify-between text-sm font-normal text-white">
            <p className="">
              {transaction.type === "buy" ? "Bought" : "Sold"}{" "}
            </p>
            <p className="">
              {transaction.profitLoss.period}{" "}
              {transaction.profitLoss.type === "profit" ? "Profit" : "Loss"}
            </p>
          </div>

          <div className="flex items-center gap-2 justify-between text-sm font-bold">
            <p className="text-white">
              Total: {transaction.total.toLocaleString()}{" "}
              {transaction.totalCurrency}{" "}
            </p>
            <p style={{ color: profitLossColor }} className="text-[#7FC9A0]">
              {profitLossSign} {Math.abs(transaction.profitLoss.percentage)}%
            </p>
          </div>
          <div className="flex items-center gap-2 justify-between text-sm font-bold">
            <div className="flex items-center gap-2.5 text-[10px] text-[#FFFFFF80] font-medium">
              <p>1USDP = 10,5546 USDC</p>
              <p>Order ID: 23456221</p>
            </div>
            <p className="text-white text-sm font-bold">
              {profitLossSign} ${" "}
              {Math.abs(transaction.profitLoss.amount).toLocaleString()}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetCard;
