import React from "react";

export type AssetTransaction = {
  id: string;
  date: string;
  type: "buy" | "sell";
  amount: number;
  currency: string;
  total: number;
  totalCurrency: string;
  exchangeRate: string;
  orderId: string;
  profitLoss: {
    type: "profit" | "loss";
    percentage: number;
    amount: number;
    period: string;
  };
};

type AssetCardProps = {
  transaction: AssetTransaction;
};

const AssetCard: React.FC<AssetCardProps> = ({ transaction }) => {
  const isProfit = transaction.profitLoss.type === "profit";
  const profitLossColor = isProfit ? "#7FC9A0" : "#FF6B6B";
  const profitLossSign = isProfit ? "+" : "-";

  return (
    <div 
      className="rounded-xl p-4 border border-[#508968] bg-[#0C341D] bg-opacity-40"
      style={{
        background: "rgba(12, 52, 29, 0.4)",
        border: "1px solid #508968",
      }}
    >
      {/* Date */}
      <div className="text-xs text-[#FFFFFF80] mb-3">
        {transaction.date}
      </div>

      {/* Main Transaction Info */}
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <div className="text-white font-medium text-base mb-1">
            {transaction.type === "buy" ? "Bought" : "Sold"} {transaction.amount.toLocaleString()} {transaction.currency}
          </div>
          <div className="text-white font-bold text-lg">
            Total: {transaction.total.toLocaleString()} {transaction.totalCurrency}
          </div>
        </div>

        <div className="text-right">
          <div className="text-[#FFFFFF80] text-sm mb-1">
            {transaction.profitLoss.period} {transaction.profitLoss.type === "profit" ? "Profit" : "Loss"}
          </div>
          <div 
            className="font-bold text-lg"
            style={{ color: profitLossColor }}
          >
            {profitLossSign} {Math.abs(transaction.profitLoss.percentage)}%
          </div>
        </div>
      </div>

      {/* Bottom Row */}
      <div className="flex justify-between items-center">
        <div className="text-[#FFFFFF80] text-xs">
          {transaction.exchangeRate} • Order ID: {transaction.orderId}
        </div>
        <div 
          className="font-bold text-base"
          style={{ color: profitLossColor }}
        >
          {profitLossSign} $ {Math.abs(transaction.profitLoss.amount).toLocaleString()}
        </div>
      </div>
    </div>
  );
};

export default AssetCard;
